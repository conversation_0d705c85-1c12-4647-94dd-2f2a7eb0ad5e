import { AxiosError, AxiosInstance } from "axios";
import FormData from "form-data";
import { type OnboardingRequest } from "../../functions/merchants/schemas/onboarding.schema.js";
import { logger } from "../../helpers/logger.js";
import { PayrixMerchantResponse, PayrixMerchantEntity, MerchantValidationResult } from "../../types/payrix.types.js";
import { createPayrixApiClient, PAYRIX_API_URL, PAYRIX_CREDENTIAL_ID } from "./api-client.js";
import { isApprovedMCCCode, getMCCCodeDetails } from "../../constants/approvedMccCodes.js";

export class PayrixMerchantService {
  private apiClient: AxiosInstance;

  constructor() {
    this.apiClient = createPayrixApiClient();
  }

  async checkMerchantExists(email: string, ein?: string): Promise<boolean> {
    try {
      logger.info("Checking for existing merchant", {
        email,
        ein: ein ? "[REDACTED]" : undefined,
      });

      const response = await this.apiClient.get(`/entities`);

      logger.info("Merchant existence check response", {
        status: response.status,
        dataLength: response.data?.response?.data?.length || 0,
      });

      const merchants: PayrixMerchantEntity[] = response.data?.response?.data || [];

      const emailExists = merchants.some((merchant: PayrixMerchantEntity) => merchant.email?.toLowerCase() === email.toLowerCase());

      return emailExists;
    } catch (error) {
      const axiosError = error as AxiosError;
      logger.error("Error checking merchant existence", {
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data,
        message: axiosError.message,
      });

      logger.warn("Merchant existence check failed, allowing onboarding to proceed");
      return false;
    }
  }

  async validateMerchantById(merchantId: string): Promise<MerchantValidationResult> {
    try {
      logger.info("Validating merchant by ID", { merchantId });

      const response = await this.apiClient.get(`/merchants/${merchantId}`);

      logger.info("Merchant validation response", {
        status: response.status,
        merchantId: response.data,
      });

      const merchant = response.data?.response?.data?.[0];

      if (!merchant) {
        return {
          isValid: false,
          error: "Merchant not found",
        };
      }

      const isActive = (merchant.status === 1 || merchant.status === 2) && merchant.inactive === 0 && merchant.frozen === 0;

      if (!isActive) {
        const statusInfo = {
          status: merchant.status,
          inactive: merchant.inactive,
          frozen: merchant.frozen,
          autoBoarded: merchant.autoBoarded,
        };

        logger.warn("Merchant status check failed", statusInfo);

        return {
          isValid: false,
          error: `Merchant is not active (status: ${merchant.status}, inactive: ${merchant.inactive}, frozen: ${merchant.frozen})`,
        };
      }

      if (merchant.mcc && !isApprovedMCCCode(merchant.mcc)) {
        const mccDetails = getMCCCodeDetails(merchant.mcc);
        logger.warn("Merchant MCC validation failed", {
          merchantId,
          mcc: merchant.mcc,
          mccDescription: mccDetails?.description,
        });

        return {
          isValid: false,
          error: `Merchant MCC code ${merchant.mcc} is not approved for payment processing. Only specific business categories are currently supported.`,
        };
      }

      logger.info("Merchant validation successful", {
        merchantId,
        status: merchant.status,
        statusType: merchant.status === 1 ? "fully-approved" : merchant.status === 2 ? "auto-boarded" : "unknown",
        inactive: merchant.inactive,
        frozen: merchant.frozen,
        autoBoarded: merchant.autoBoarded,
        dba: merchant.dba,
        mcc: merchant.mcc,
      });

      return {
        isValid: true,
        merchant,
      };
    } catch (error) {
      const axiosError = error as AxiosError;
      logger.error("Error validating merchant by ID", {
        merchantId,
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data,
        message: axiosError.message,
      });

      if (axiosError.response?.status === 404) {
        return {
          isValid: false,
          error: "Merchant not found",
        };
      }

      return {
        isValid: false,
        error: `Validation failed: ${axiosError.message}`,
      };
    }
  }

  async createMerchant(merchantData: OnboardingRequest): Promise<PayrixMerchantResponse> {
    try {
      logger.info("Sending request to Payrix API", {
        url: `${PAYRIX_API_URL}/entities`,
        headers: { "Content-Type": "application/json", APIKEY: "[REDACTED]" },
        merchantEmail: merchantData.email,
        merchantName: merchantData.name,
        hasBankVerification: !!merchantData.bankVerification,
        bankVerificationMethod: merchantData.bankVerification?.verificationMethod,
        // Sensitive data (SSN, bank details, file content) intentionally excluded for PCI compliance
        // data: merchantData,
      });

      logger.info("Merchant data check", {
        merchantData,
      });

      const response = await this.apiClient.post("/entities", merchantData);

      logger.info("Payrix API response", {
        status: response.status,
        data: response.data,
      });

      const entityData = response.data?.response?.data?.[0];
      if (!entityData) {
        throw new Error("Invalid Payrix response structure: no entity data found");
      }

      return entityData;
    } catch (error) {
      const axiosError = error as AxiosError;
      logger.error("Payrix API Error", {
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data,
        message: axiosError.message,
      });

      throw new Error(`Payrix API Error (${axiosError.response?.status}): ${JSON.stringify(axiosError.response?.data || axiosError.message)}`);
    }
  }

  async createNote(noteData: { entity: string; note: string; type?: string; login?: string }): Promise<{ id: string }> {
    try {
      logger.info("Creating note in Payrix", {
        entity: noteData.entity,
        type: noteData.type,
        noteLength: noteData.note.length,
      });

      const response = await this.apiClient.post("/notes", noteData);

      logger.info("Payrix note creation response", {
        status: response.status,
        data: response.data,
      });

      const noteData_response = response.data?.response?.data?.[0];
      if (!noteData_response?.id) {
        throw new Error("Invalid Payrix response structure: no note ID found");
      }

      return { id: noteData_response.id };
    } catch (error) {
      logger.error("Error creating note in Payrix", { error });
      if (error instanceof AxiosError) {
        logger.error("Payrix API error details", {
          status: error.response?.status,
          data: error.response?.data,
          message: error.message,
        });
      }
      throw error;
    }
  }

  async createPlaidLinkToken(linkTokenData: {
    userId: string;
    countryCode: string;
    redirectUri: string;
  }): Promise<{ linkToken: string; requestId: string }> {
    try {
      logger.info("Creating Plaid link token via Payrix", {
        userId: linkTokenData.userId,
        countryCode: linkTokenData.countryCode,
        redirectUri: linkTokenData.redirectUri,
      });

      const response = await this.apiClient.post("/plaid/linkToken/create", linkTokenData);

      logger.info("Plaid link token creation response", {
        status: response.status,
        hasLinkToken: !!response.data?.Response?.responses?.[0]?.linkToken,
        requestId: response.data?.Response?.responses?.[0]?.requestId,
      });

      const linkTokenResponse = response.data?.Response?.responses?.[0];
      if (!linkTokenResponse?.linkToken) {
        throw new Error("Invalid Payrix response structure: no link token found");
      }

      return {
        linkToken: linkTokenResponse.linkToken,
        requestId: linkTokenResponse.requestId,
      };
    } catch (error) {
      logger.error("Error creating Plaid link token", {
        error,
        userId: linkTokenData.userId,
        countryCode: linkTokenData.countryCode,
      });

      if (error instanceof AxiosError) {
        logger.error("Payrix API error details", {
          status: error.response?.status,
          data: error.response?.data,
          message: error.message,
        });

        const status = error.response?.status;
        if (status === 400) {
          throw new Error("Invalid request parameters for Plaid link token creation");
        } else if (status === 401) {
          throw new Error("Authentication failed with Payrix API");
        } else if (status === 403) {
          throw new Error("Plaid integration not enabled for this account");
        }
      }

      throw error;
    }
  }

  async createNoteDocument(documentData: {
    note: string;
    file: { filename: string; content: Buffer; contentType: string };
    description?: string;
  }): Promise<{ id: string }> {
    try {
      logger.info("Creating note document in Payrix (3-step process)", {
        noteId: documentData.note,
        fileName: documentData.file.filename,
        fileSize: documentData.file.content.length,
        contentType: documentData.file.contentType,
      });

      // Step 1: Create note document metadata
      const fileExtension = documentData.file.filename.split(".").pop()?.toLowerCase() || "png";

      const noteDocumentPayload = {
        note: documentData.note,
        type: fileExtension,
        documentType: "voidCheck",
        description: documentData.description || "Void check for bank account verification",
        name: documentData.file.filename,
      };

      logger.info("Step 1: Creating note document metadata", {
        noteId: documentData.note,
        payload: noteDocumentPayload,
      });

      const noteDocResponse = await this.apiClient.post("/noteDocuments", noteDocumentPayload);

      logger.info("Note document metadata created", {
        status: noteDocResponse.status,
        responseData: noteDocResponse.data,
      });

      const noteDocumentId = noteDocResponse.data?.response?.data?.[0]?.id;
      if (!noteDocumentId) {
        throw new Error("Failed to create note document metadata - no ID returned");
      }

      // Step 2: Upload file to the note document
      logger.info("Step 2: Uploading file to note document", {
        noteDocumentId,
        fileName: documentData.file.filename,
      });

      const formData = new FormData();

      // Add file
      formData.append("file", documentData.file.content, {
        filename: documentData.file.filename,
        contentType: documentData.file.contentType,
      });

      // Add required Payrix fields for file upload
      const uploadPayload = {
        credential: PAYRIX_CREDENTIAL_ID || "default_credential",
        integration: "WORLDPAY",
        direction: "upload",
        name: documentData.file.filename,
        description: documentData.description || "Void Check for Bank Account Verification",
      };

      formData.append("json", JSON.stringify(uploadPayload));

      const fileUploadResponse = await this.apiClient.post(`/files/noteDocuments/${noteDocumentId}`, formData, {
        headers: {
          ...formData.getHeaders(),
        },
      });

      logger.info("File uploaded successfully to Payrix", {
        noteDocumentId,
        fileName: documentData.file.filename,
        uploadStatus: fileUploadResponse.status,
      });

      return { id: noteDocumentId };
    } catch (error) {
      logger.error("Error in createNoteDocument 3-step process", {
        error,
        noteId: documentData.note,
        fileName: documentData.file.filename,
      });

      if (error instanceof AxiosError) {
        logger.error("Payrix API error details", {
          status: error.response?.status,
          data: error.response?.data,
          message: error.message,
        });

        // Provide more specific error messages based on Payrix response
        const status = error.response?.status;
        const payrixData = error.response?.data;

        if (status === 400) {
          throw new Error(`Payrix validation error: ${payrixData?.message || "Invalid request data"}`);
        } else if (status === 401 || status === 403) {
          throw new Error("Payrix authentication failed. Please check API credentials.");
        } else if (status === 404) {
          throw new Error("Note not found. Please ensure the note exists before uploading documents.");
        } else if (status === 413) {
          throw new Error("File too large for Payrix. Please use a smaller file.");
        } else if (status === 422) {
          throw new Error(`Payrix processing error: ${payrixData?.message || "Unable to process request"}`);
        } else if (status && status >= 500) {
          throw new Error("Payrix service temporarily unavailable. Please try again later.");
        }
      }

      throw error;
    }
  }
}
