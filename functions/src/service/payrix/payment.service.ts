import { AxiosError, AxiosInstance } from "axios";
import { logger } from "../../helpers/logger.js";
import { TokenPaymentData, PaymentResult, TokenDeletionResult } from "../../types/payrix.types.js";
import { createPayrixApiClient, PAYRIX_API_URL } from "./api-client.js";

export class PayrixPaymentService {
  private apiClient: AxiosInstance;

  constructor() {
    this.apiClient = createPayrixApiClient();
  }

  async processTokenPayment(paymentData: TokenPaymentData): Promise<PaymentResult> {
    try {
      logger.info("Processing token payment", {
        merchantId: paymentData.merchantId,
        token: paymentData.token.substring(0, 8) + "...",
        amount: paymentData.amount,
        description: paymentData.description,
      });

      const transactionData = {
        merchant: paymentData.merchantId,
        type: "1",
        origin: "2",
        token: paymentData.token,
        total: paymentData.amount.toString(),
        description: paymentData.description || "Token-based payment",
        ...(paymentData.customerInfo?.email && { email: paymentData.customerInfo.email }),
        ...(paymentData.customerInfo?.name && { name: paymentData.customerInfo.name }),
        ...(paymentData.customerInfo?.address && {
          address1: paymentData.customerInfo.address.line1,
          address2: paymentData.customerInfo.address.line2,
          city: paymentData.customerInfo.address.city,
          state: paymentData.customerInfo.address.state,
          zip: paymentData.customerInfo.address.zip,
          country: paymentData.customerInfo.address.country,
        }),
      };

      logger.info("Sending transaction request to Payrix", {
        url: `${PAYRIX_API_URL}/txns`,
        merchantId: paymentData.merchantId,
        amount: paymentData.amount,
        token: paymentData.token.substring(0, 8) + "...",
      });

      const response = await this.apiClient.post("/txns", transactionData);

      logger.info("Token payment response received", {
        status: response.status,
        transactionId: response.data?.response?.data?.[0]?.id,
        transactionStatus: response.data?.response?.data?.[0]?.status,
      });

      const transactionData_response = response.data?.response?.data?.[0];
      if (!transactionData_response) {
        throw new Error("Invalid Payrix response structure: no transaction data found");
      }

      return {
        success: true,
        transaction: transactionData_response,
      };
    } catch (error) {
      const axiosError = error as AxiosError;
      logger.error("Token payment processing error", {
        merchantId: paymentData.merchantId,
        token: paymentData.token.substring(0, 8) + "...",
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data,
        message: axiosError.message,
      });

      const errorData = axiosError.response?.data as { message?: string } | undefined;
      return {
        success: false,
        error: `Payment processing failed: ${errorData?.message || axiosError.message}`,
      };
    }
  }

  async deleteToken(token: string): Promise<TokenDeletionResult> {
    try {
      logger.info("Deleting token", {
        token: token.substring(0, 8) + "...",
      });

      const response = await this.apiClient.delete(`/tokens/${token}`);

      logger.info("Token deletion successful", {
        token: token.substring(0, 8) + "...",
        status: response.status,
      });

      return { success: true };
    } catch (error) {
      const axiosError = error as AxiosError;
      logger.error("Token deletion error", {
        token: token.substring(0, 8) + "...",
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data,
        message: axiosError.message,
      });

      const errorData = axiosError.response?.data as { message?: string } | undefined;
      return {
        success: false,
        error: `Token deletion failed: ${errorData?.message || axiosError.message}`,
      };
    }
  }
}
