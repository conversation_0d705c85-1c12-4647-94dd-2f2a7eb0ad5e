import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { fillDemoData, nextStep, updateFormData } from "../../redux/slices/onboardingSlice.ts";
import { type RootState } from "../../redux/store.ts";
import { toast } from "sonner";
import { BusinessDetailsSection, ContactSection, AddressSection } from "./sections";
import { validateBusinessInfoForm, ValidationErrors } from "./utils/validation";
import { BUSINESS_TYPE_CHECKS } from "./constants/businessConstants";

const BusinessInfoForm = () => {
  const dispatch = useDispatch();
  const { formData } = useSelector((state: RootState) => state.onboarding);
  const [errors, setErrors] = useState<ValidationErrors>({});

  const selectedBusinessType = formData.type;
  const requiresCorporateStructure = selectedBusinessType && BUSINESS_TYPE_CHECKS.requiresCorporateStructure(selectedBusinessType);

  useEffect(() => {
    if (Object.keys(errors).length > 0) {
      const newErrors = { ...errors };
      let hasChanges = false;

      if (!requiresCorporateStructure && newErrors.dba) {
        delete newErrors.dba;
        hasChanges = true;
      }

      // Clear Annual Sales error when checkbox is checked (new = 1)
      if (formData.merchant?.new === 1 && newErrors.annualCCSales) {
        delete newErrors.annualCCSales;
        hasChanges = true;
      }

      if (hasChanges) {
        setErrors(newErrors);
      }
    }
  }, [selectedBusinessType, requiresCorporateStructure, formData.merchant?.new, errors]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const validationErrors = validateBusinessInfoForm(formData);
    setErrors(validationErrors);

    if (Object.keys(validationErrors).length === 0) {
      dispatch(nextStep());
    } else {
      const errorCount = Object.keys(validationErrors).length;
      toast.error(`Please fix ${errorCount} validation error${errorCount > 1 ? "s" : ""} before continuing.`);
    }
  };

  const handleChange = (field: string, value: string | number) => {
    if (field.startsWith("merchant.")) {
      const merchantField = field.replace("merchant.", "");

      // Prevent manual changes to annualCCSales when new=1 (first time digital payments)
      if (merchantField === "annualCCSales" && formData.merchant?.new === 1) {
        return; // Ignore the change
      }

      // Create the base merchant update
      const merchantUpdate = {
        dba: "",
        new: 1,
        mcc: "",
        status: "1",
        members: [],
        ...formData.merchant,
        [merchantField]: value,
      };

      if (merchantField === "new") {
        if (value === 1) {
          merchantUpdate.annualCCSales = 0;
        } else {
          merchantUpdate.annualCCSales = undefined;
        }
      }

      dispatch(
        updateFormData({
          merchant: merchantUpdate,
        })
      );
    } else {
      const updates: Record<string, string | number | object> = { [field]: value };

      if (field === "name" && requiresCorporateStructure && !formData.merchant?.dba) {
        updates.merchant = {
          new: 1,
          mcc: "",
          status: "1",
          ...formData.merchant,
          dba: value as string,
        };
      }

      if (field === "type") {
        // Auto-set entity classification based on business type, but allow user override
        const businessType = value as number;
        const { isPublicEntity } = BUSINESS_TYPE_CHECKS;

        // Set default classification based on business type
        if (isPublicEntity(businessType)) {
          updates.public = 1; // Public for Non-Profit and Government
        } else {
          updates.public = 0; // Private for all others
        }
      }

      dispatch(updateFormData(updates));
    }
  };

  const handleFillDemoData = () => {
    dispatch(fillDemoData());
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="border-b border-gray-200 px-8 py-6 flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-semibold text-gray-900">Business Information</h1>
              <p className="text-gray-600 mt-1">Tell us about your business</p>
            </div>
            <button
              type="button"
              onClick={handleFillDemoData}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center gap-2 transition-colors"
              title="Fill form with demo data for testing"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              Fill Demo Data
            </button>
          </div>

          <form onSubmit={handleSubmit} className="px-8 py-8">
            <BusinessDetailsSection formData={formData} errors={errors} onChange={handleChange} />
            <ContactSection formData={formData} errors={errors} onChange={handleChange} />
            <AddressSection formData={formData} errors={errors} onChange={handleChange} />

            <div className="border-t border-gray-200 pt-6 flex justify-end">
              <button
                type="submit"
                className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 transition-all duration-200"
              >
                Continue to Owner Information
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default BusinessInfoForm;
