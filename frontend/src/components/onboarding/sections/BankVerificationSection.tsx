import { useState, useEffect } from "react";
import { FileUpload } from "../form-fields";
import { VERIFICATION_METHODS, type VerificationMethod } from "../constants/bankAccountConstants";
import { BankAccountValidationErrors } from "../utils/validation";
import { usePlaidLinkIntegration, PlaidAccountData } from "../../../hooks/usePlaidLink";
import { toast } from "sonner";

interface BankVerificationSectionProps {
  verificationMethod: VerificationMethod;
  onVerificationMethodChange: (method: VerificationMethod) => void;
  uploadedFile: File | null;
  onFileChange: (file: File | null) => void;
  uploadPreview: string | null;
  onPreviewChange: (preview: string | null) => void;
  errors: BankAccountValidationErrors;
  onPlaidSuccess?: (accountData: PlaidAccountData) => void;
}

export const BankVerificationSection = ({
  verificationMethod,
  onVerificationMethodChange,
  uploadedFile,
  onFileChange,
  uploadPreview,
  onPreviewChange,
  errors,
  onPlaidSuccess,
}: BankVerificationSectionProps) => {
  const [plaidAccountData, setPlaidAccountData] = useState<PlaidAccountData | null>(null);

  // Plaid Link integration
  const plaidLink = usePlaidLinkIntegration({
    userId: `user_${Date.now()}`, // Generate unique user ID for this session
    countryCode: "US",
    redirectUri: window.location.origin + "/onboarding",
    onSuccess: (accountData) => {
      setPlaidAccountData(accountData);
      onPlaidSuccess?.(accountData);
      toast.success("Bank account connected successfully!");
    },
    onError: (error) => {
      console.error("Plaid Link error:", error);
      toast.error("Failed to connect bank account", {
        description: "Please try again or use manual verification",
      });
    },
  });

  // Initialize Plaid when component mounts or when Plaid method is selected
  useEffect(() => {
    if (verificationMethod === VERIFICATION_METHODS.PLAID && !plaidLink.ready && !plaidLink.loading) {
      plaidLink.initializePlaid();
    }
  }, [verificationMethod, plaidLink]);

  const handlePlaidLinkClick = () => {
    if (!plaidLink.ready) {
      toast.info("Initializing bank verification...");
      plaidLink.initializePlaid();
      return;
    }
    if (plaidLink.open) {
      plaidLink.open();
    }
  };
  return (
    <div className="mb-10">
      <h2 className="text-lg font-medium text-gray-900 mb-6">Bank Account Verification</h2>

      <div className="bg-amber-50 border border-amber-200 rounded-lg p-6 mb-6">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <svg className="w-5 h-5 text-amber-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <div>
            <h3 className="text-sm font-medium text-amber-900 mb-1">Bank Verification Required</h3>
            <div className="text-sm text-amber-800 space-y-1">
              <p>• Your bank account will need to be verified before processing payments</p>
              <p>• Verification typically takes 1-2 business days</p>
              <p>• You can upload a void check now or verify manually later</p>
            </div>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <label className="relative flex items-start p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
          <input
            type="radio"
            name="verificationMethod"
            value={VERIFICATION_METHODS.PLAID}
            checked={verificationMethod === VERIFICATION_METHODS.PLAID}
            onChange={() => onVerificationMethodChange(VERIFICATION_METHODS.PLAID)}
            className="mt-1 text-blue-600 border-gray-300 focus:ring-blue-500"
          />
          <div className="ml-3 flex-1">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="block text-sm font-medium text-gray-900">Verify with Plaid</span>
                {plaidAccountData && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">✓ Connected</span>
                )}
              </div>
              {verificationMethod === VERIFICATION_METHODS.PLAID && (
                <button
                  type="button"
                  onClick={handlePlaidLinkClick}
                  disabled={plaidLink.loading}
                  className="ml-4 bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {plaidLink.loading ? "Initializing..." : plaidAccountData ? "Change Account" : "Connect Bank"}
                </button>
              )}
            </div>
            <span className="block text-sm text-gray-500">Instantly verify your bank account using Plaid&apos;s secure connection</span>
            {plaidAccountData && (
              <div className="mt-2 p-3 bg-green-50 border border-green-200 rounded-lg">
                <div className="text-sm text-green-800">
                  <div className="font-medium">{plaidAccountData.institutionName}</div>
                  <div>
                    {plaidAccountData.accountName} ending in {plaidAccountData.accountMask}
                  </div>
                  {plaidAccountData.verificationStatus && (
                    <div className="text-xs text-green-600 mt-1">Status: {plaidAccountData.verificationStatus}</div>
                  )}
                </div>
              </div>
            )}
            {plaidLink.error && (
              <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                <div className="text-sm text-red-800">{plaidLink.error}</div>
              </div>
            )}
          </div>
        </label>

        <label className="relative flex items-start p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
          <input
            type="radio"
            name="verificationMethod"
            value={VERIFICATION_METHODS.MANUAL}
            checked={verificationMethod === VERIFICATION_METHODS.MANUAL}
            onChange={() => onVerificationMethodChange(VERIFICATION_METHODS.MANUAL)}
            className="mt-1 text-blue-600 border-gray-300 focus:ring-blue-500"
          />
          <div className="ml-3">
            <span className="block text-sm font-medium text-gray-900">Manual Verification</span>
            <span className="block text-sm text-gray-500">Upload a voided check for manual verification of your bank account</span>
          </div>
        </label>
      </div>

      {verificationMethod === VERIFICATION_METHODS.MANUAL && (
        <div className="mt-6">
          <FileUpload
            label="Void Check Upload"
            value={uploadedFile}
            onChange={onFileChange}
            preview={uploadPreview}
            onPreviewChange={onPreviewChange}
            error={errors.voidCheck}
            accept="image/jpeg,image/jpg,image/png,application/pdf"
            maxSizeMB={10}
            placeholder="Upload a voided check"
            hint="JPEG, PNG, or PDF up to 10MB"
          />
        </div>
      )}
    </div>
  );
};
