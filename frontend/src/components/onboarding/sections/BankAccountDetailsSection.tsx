import { SelectInput } from "../form-fields";
import { getAccountMethods } from "../constants/bankAccountConstants";

interface BankAccountData {
  account: {
    routing?: string;
    number?: string;
    method?: number;
  };
}

interface BankAccountDetailsSectionProps {
  account: BankAccountData;
  onChange: (field: string, value: string | number) => void;
  isSoleProprietor: boolean;
}

export const BankAccountDetailsSection = ({ account, onChange, isSoleProprietor }: BankAccountDetailsSectionProps) => {
  const accountMethods = getAccountMethods(isSoleProprietor);

  return (
    <div className="mb-10">
      <h2 className="text-lg font-medium text-gray-900 mb-6">Bank Account Details</h2>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <SelectInput
          label="Account Type"
          value={account.account.method || (isSoleProprietor ? 8 : 10)}
          onChange={(value) => onChange("account.method", value)}
          options={accountMethods}
          required
          className="lg:col-span-2"
        />
      </div>
    </div>
  );
};
